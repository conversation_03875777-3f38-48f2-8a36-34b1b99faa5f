@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* 自定义动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes celebration {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes progress-fill {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* 自定义类 */
.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-celebration {
  animation: celebration 0.8s ease-in-out;
}

.gradient-flow {
  background: linear-gradient(-45deg, #3b82f6, #1d4ed8, #2563eb, #1e40af);
  background-size: 400% 400%;
  animation: gradient-flow 3s ease infinite;
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.connection-line-glow {
  filter: drop-shadow(0 0 3px currentColor);
}

.button-press {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.progress-bar {
  transform-origin: left;
  animation: progress-fill 0.3s ease-out;
}

/* 响应式增强 */
@media (max-width: 768px) {
  .animate-slide-in-up {
    animation-duration: 0.3s;
  }

  .gradient-flow {
    animation-duration: 2s;
  }

  /* 移动端触摸优化 */
  .button-press {
    transform: scale(0.98);
  }

  /* 移动端字体调整 */
  .mobile-text-adjust {
    font-size: 0.9rem;
  }
}

@media (max-width: 640px) {
  /* 小屏幕优化 */
  .animate-celebration {
    animation-duration: 0.6s;
  }

  .animate-bounce-in {
    animation-duration: 0.4s;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .connection-line-glow {
    filter: none;
  }

  .glass-effect {
    backdrop-filter: none;
    background: rgba(255, 255, 255, 0.9);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in-up,
  .animate-slide-in-left,
  .animate-pulse-glow,
  .animate-shake,
  .animate-bounce-in,
  .animate-celebration,
  .gradient-flow {
    animation: none;
  }

  .progress-bar {
    animation: none;
    transform: scaleX(1);
  }
}
