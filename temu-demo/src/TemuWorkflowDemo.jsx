import React, { useState, useEffect } from 'react';
import { Play, RefreshCw, CheckCircle, XCircle, Clock, AlertCircle, Loader2 } from 'lucide-react';

const TemuWorkflowDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed
  const [visibleSteps, setVisibleSteps] = useState(1); // 控制显示的步骤数量
  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤

  // 工作流步骤定义
  const workflowSteps = [
    {
      id: 'login',
      name: '登录',
      runningDescription: '正在验证登录信息...',
      completedDescription: '已完成登录【Temu账号】名下的店铺1',
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'product_filter',
      name: '商品筛选', 
      runningDescription: '正在筛选符合条件的商品...',
      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',
      duration: 3000,
      status: 'pending'
    },
    {
      id: 'product_processing',
      name: '商品加权',
      runningDescription: '正在为商品设置流量加权...',
      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',
      duration: 4000,
      status: 'pending'
    },
    {
      id: 'result',
      name: '结果',
      runningDescription: '正在生成执行结果...',
      completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',
      duration: 1000,
      status: 'pending'
    }
  ];

  const [steps, setSteps] = useState(workflowSteps);

  // 获取状态图标
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300 bg-gray-100"></div>;
    }
  };

  // 获取步骤描述
  const getStepDescription = (step) => {
    if (step.status === 'running') {
      return step.runningDescription;
    } else if (step.status === 'completed') {
      return step.completedDescription;
    } else if (step.status === 'failed') {
      return `执行失败：${step.runningDescription}`;
    }
    return '等待执行...';
  };

  // 计算当前已完成的步骤数
  const getCompletedSteps = () => {
    return steps.filter(step => step.status === 'completed').length;
  };

  // 模拟工作流执行
  const executeWorkflow = async () => {
    setIsRunning(true);
    setWorkflowStatus('running');
    setFailedStep(null);

    // 从当前失败步骤或第一步开始
    const startStep = failedStep !== null ? failedStep : 0;
    setCurrentStep(startStep);

    // 如果是重新开始，重置步骤状态
    if (failedStep === null) {
      setVisibleSteps(1);
      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));
    }

    for (let i = startStep; i < steps.length; i++) {
      setCurrentStep(i);
      
      // 显示当前步骤（如果还未显示）
      if (i + 1 > visibleSteps) {
        setVisibleSteps(i + 1);
        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤
      }
      
      // 设置当前步骤为运行中
      setSteps(prev => prev.map((step, index) => 
        index === i ? { ...step, status: 'running' } : step
      ));

      // 等待步骤完成
      await new Promise(resolve => setTimeout(resolve, steps[i].duration));

      // 随机失败演示（15%概率，在第2或第3步）
      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);

      if (shouldFail) {
        setSteps(prev => prev.map((step, index) => 
          index === i ? { ...step, status: 'failed' } : step
        ));
        setWorkflowStatus('failed');
        setFailedStep(i);
        setIsRunning(false);
        return;
      }

      // 设置步骤为完成
      setSteps(prev => prev.map((step, index) => 
        index === i ? { ...step, status: 'completed' } : step
      ));

      // 完成一步后显示下一步（如果不是最后一步）
      if (i < steps.length - 1) {
        setVisibleSteps(i + 2);
        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步
      }
    }

    setWorkflowStatus('completed');
    setFailedStep(null);
    setIsRunning(false);
  };

  // 重试失败的步骤
  const retryFromFailed = () => {
    if (failedStep !== null) {
      // 重置失败步骤的状态
      setSteps(prev => prev.map((step, index) => 
        index === failedStep ? { ...step, status: 'pending' } : step
      ));
      executeWorkflow();
    }
  };

  // 重置整个工作流
  const resetWorkflow = () => {
    setIsRunning(false);
    setWorkflowStatus('idle');
    setCurrentStep(0);
    setVisibleSteps(1);
    setFailedStep(null);
    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gray-50 min-h-screen">
      {/* AI对话框容器 */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* 对话框头部 */}
        <div className="bg-blue-500 text-white p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-semibold">Temu自动化助手</h3>
              <p className="text-blue-100 text-sm">流量加速自动化执行中...</p>
            </div>
          </div>
        </div>

        {/* 对话内容 */}
        <div className="p-6">
          {/* AI消息 */}
          <div className="mb-6">
            <div className="bg-gray-100 rounded-lg p-4 mb-4">
              <p className="text-gray-800 mb-2">好的，开始执行计划</p>
              <div className="text-sm text-gray-600 bg-white rounded-lg p-3 border">
                <span className="font-medium">执行配置：</span>高级流量加权档位，价格范围4-6美元，时效30天
              </div>
            </div>
          </div>

          {/* 优化后的时间线步骤列表 - 完美连接线 */}
          <div className="relative">
            {steps.slice(0, visibleSteps).map((step, index) => (
              <div key={step.id} className="relative">
                {/* 连接线 - 绝对定位，从当前图标中心延伸到下一个图标中心 */}
                {index < visibleSteps - 1 && (
                  <div 
                    className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${
                      step.status === 'completed' ? 'bg-green-500' :
                      step.status === 'failed' ? 'bg-red-500' :
                      step.status === 'running' ? 'bg-blue-500' :
                      'bg-gray-300'
                    }`}
                    style={{
                      height: 'calc(100% + 1.5rem)' // 精确延伸到下一个步骤的图标位置
                    }}
                  ></div>
                )}
                
                {/* 步骤内容容器 */}
                <div className="flex items-start space-x-4 relative z-10 pb-6">
                  {/* 状态图标容器 - 优化层级确保完美覆盖 */}
                  <div className="flex-shrink-0 relative">
                    {/* 图标背景圆圈 - 确保完全覆盖连接线 */}
                    <div className="w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100"></div>
                    
                    {/* 状态图标 - 最高层级 */}
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${
                      step.status === 'completed' ? 'bg-green-500 text-white' :
                      step.status === 'failed' ? 'bg-red-500 text-white' :
                      step.status === 'running' ? 'bg-blue-500 text-white' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      {step.status === 'completed' && <CheckCircle className="w-5 h-5" />}
                      {step.status === 'failed' && <XCircle className="w-5 h-5" />}
                      {step.status === 'running' && <Loader2 className="w-5 h-5 animate-spin" />}
                      {step.status === 'pending' && <div className="w-3 h-3 rounded-full bg-gray-600"></div>}
                    </div>
                  </div>
                  
                  {/* 步骤内容 */}
                  <div className="flex-1 min-w-0 pt-1">
                    <div className="mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{step.name}</h4>
                      {/* 失败状态的重试按钮 */}
                      {step.status === 'failed' && (
                        <button
                          onClick={retryFromFailed}
                          disabled={isRunning}
                          className="mt-2 text-xs bg-orange-500 hover:bg-orange-600 disabled:opacity-50 text-white px-3 py-1 rounded-full font-medium transition-colors"
                        >
                          重试
                        </button>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 leading-relaxed">
                      {getStepDescription(step)}
                    </p>
                    
                    {/* 失败时的错误信息 */}
                    {step.status === 'failed' && (
                      <div className="mt-2 bg-red-50 border border-red-200 rounded-lg p-3">
                        <p className="text-red-700 text-sm">
                          执行失败，可能是网络连接问题或系统繁忙，请点击重试继续执行。
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮区域 */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {workflowStatus === 'idle' && (
                  <button
                    onClick={executeWorkflow}
                    disabled={isRunning}
                    className="bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white px-6 py-3 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                  >
                    <Play className="w-4 h-4" />
                    <span>开始执行</span>
                  </button>
                )}

                {workflowStatus === 'running' && (
                  <button
                    disabled
                    className="bg-gray-400 text-white px-6 py-3 rounded-lg text-sm font-medium cursor-not-allowed flex items-center space-x-2"
                  >
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>执行中...</span>
                  </button>
                )}

                {workflowStatus === 'completed' && (
                  <button
                    onClick={resetWorkflow}
                    className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                  >
                    <CheckCircle className="w-4 h-4" />
                    <span>执行完成</span>
                  </button>
                )}

                {workflowStatus === 'failed' && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={retryFromFailed}
                      disabled={isRunning}
                      className="bg-orange-500 hover:bg-orange-600 disabled:opacity-50 text-white px-6 py-3 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>重试</span>
                    </button>
                    <button
                      onClick={resetWorkflow}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
                    >
                      重新开始
                    </button>
                  </div>
                )}

                {/* 整体进度指示 */}
                {workflowStatus === 'running' && (
                  <div className="flex items-center space-x-3 bg-blue-50 rounded-lg px-4 py-2 border border-blue-200">
                    <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                    <span className="text-sm text-blue-700 font-medium">
                      正在执行第 {currentStep + 1} 步
                    </span>
                  </div>
                )}
              </div>

              {/* 刷新按钮 */}
              <button
                onClick={resetWorkflow}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                title="重新开始"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>

            {/* 状态指示器 */}
            {workflowStatus === 'completed' && (
              <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-green-700 font-medium">
                    🎉 自动化执行完成！所有商品已成功配置流量加速
                  </span>
                </div>
              </div>
            )}

            {workflowStatus === 'failed' && failedStep !== null && (
              <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <span className="text-red-700 font-medium">
                    执行中断，第{failedStep + 1}步出现问题，可点击重试继续执行
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Demo控制面板 */}
      <div className="mt-6 bg-white rounded-lg shadow p-4">
        <h4 className="font-medium text-gray-900 mb-2">Demo控制面板</h4>
        <p className="text-sm text-gray-600 mb-3">
          演示特性：✅ 动态描述变化 ✅ 逐步显示时间线 ✅ 失败重试功能 ✅ 完美连接线
        </p>
        <div className="flex space-x-2">
          <button
            onClick={executeWorkflow}
            disabled={isRunning}
            className="bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            🚀 模拟执行
          </button>
          <button
            onClick={resetWorkflow}
            className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
          >
            🔄 重置状态
          </button>
          <button
            onClick={() => {
              // 快速演示失败场景
              setVisibleSteps(3);
              setSteps(prev => prev.map((step, index) => {
                if (index === 0) return { ...step, status: 'completed' };
                if (index === 1) return { ...step, status: 'completed' };
                if (index === 2) return { ...step, status: 'failed' };
                return step;
              }));
              setWorkflowStatus('failed');
              setFailedStep(2);
            }}
            className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-sm"
          >
            ⚠️ 演示失败
          </button>
        </div>
      </div>
    </div>
  );
};

export default TemuWorkflowDemo;
