import React, { useState, useEffect } from 'react';
import { Play, RefreshCw, CheckCircle, XCircle, Clock, AlertCircle, Loader2 } from 'lucide-react';

const TemuWorkflowDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed
  const [visibleSteps, setVisibleSteps] = useState(1); // 控制显示的步骤数量
  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤
  const [buttonPressed, setButtonPressed] = useState(null); // 按钮按下状态
  const [stepAnimations, setStepAnimations] = useState({}); // 步骤动画状态
  const [showCelebration, setShowCelebration] = useState(false); // 庆祝动画状态

  // 工作流步骤定义
  const workflowSteps = [
    {
      id: 'login',
      name: '登录',
      runningDescription: '正在验证登录信息...',
      completedDescription: '已完成登录【Temu账号】名下的店铺1',
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'product_filter',
      name: '商品筛选', 
      runningDescription: '正在筛选符合条件的商品...',
      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',
      duration: 3000,
      status: 'pending'
    },
    {
      id: 'product_processing',
      name: '商品加权',
      runningDescription: '正在为商品设置流量加权...',
      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',
      duration: 4000,
      status: 'pending'
    },
    {
      id: 'result',
      name: '结果',
      runningDescription: '正在生成执行结果...',
      completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',
      duration: 1000,
      status: 'pending'
    }
  ];

  const [steps, setSteps] = useState(workflowSteps);

  // 获取状态图标
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300 bg-gray-100"></div>;
    }
  };

  // 获取步骤描述
  const getStepDescription = (step) => {
    if (step.status === 'running') {
      return step.runningDescription;
    } else if (step.status === 'completed') {
      return step.completedDescription;
    } else if (step.status === 'failed') {
      return `执行失败：${step.runningDescription}`;
    }
    return '等待执行...';
  };

  // 计算当前已完成的步骤数
  const getCompletedSteps = () => {
    return steps.filter(step => step.status === 'completed').length;
  };

  // 模拟工作流执行
  const executeWorkflow = async () => {
    setIsRunning(true);
    setWorkflowStatus('running');
    setFailedStep(null);
    setShowCelebration(false);

    // 从当前失败步骤或第一步开始
    const startStep = failedStep !== null ? failedStep : 0;
    setCurrentStep(startStep);

    // 如果是重新开始，重置步骤状态
    if (failedStep === null) {
      setVisibleSteps(1);
      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));
      setStepAnimations({});
    }

    for (let i = startStep; i < steps.length; i++) {
      setCurrentStep(i);

      // 显示当前步骤（如果还未显示）
      if (i + 1 > visibleSteps) {
        setVisibleSteps(i + 1);
        // 添加步骤出现动画
        setStepAnimations(prev => ({ ...prev, [i]: 'slide-in' }));
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // 设置当前步骤为运行中，添加脉冲动画
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'running' } : step
      ));
      setStepAnimations(prev => ({ ...prev, [i]: 'pulse' }));

      // 等待步骤完成
      await new Promise(resolve => setTimeout(resolve, steps[i].duration));

      // 随机失败演示（15%概率，在第2或第3步）
      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);

      if (shouldFail) {
        setSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'failed' } : step
        ));
        setStepAnimations(prev => ({ ...prev, [i]: 'shake' }));
        setWorkflowStatus('failed');
        setFailedStep(i);
        setIsRunning(false);
        return;
      }

      // 设置步骤为完成，添加弹跳动画
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'completed' } : step
      ));
      setStepAnimations(prev => ({ ...prev, [i]: 'bounce' }));

      // 完成一步后显示下一步（如果不是最后一步）
      if (i < steps.length - 1) {
        setVisibleSteps(i + 2);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setWorkflowStatus('completed');
    setFailedStep(null);
    setIsRunning(false);

    // 触发庆祝动画
    setShowCelebration(true);
    setTimeout(() => setShowCelebration(false), 2000);
  };

  // 重试失败的步骤
  const retryFromFailed = () => {
    if (failedStep !== null) {
      // 重置失败步骤的状态
      setSteps(prev => prev.map((step, index) => 
        index === failedStep ? { ...step, status: 'pending' } : step
      ));
      executeWorkflow();
    }
  };

  // 重置整个工作流
  const resetWorkflow = () => {
    setIsRunning(false);
    setWorkflowStatus('idle');
    setCurrentStep(0);
    setVisibleSteps(1);
    setFailedStep(null);
    setShowCelebration(false);
    setStepAnimations({});
    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));
  };

  // 按钮点击处理函数
  const handleButtonClick = (buttonId, action) => {
    setButtonPressed(buttonId);
    setTimeout(() => setButtonPressed(null), 150);
    action();
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
      {/* AI对话框容器 */}
      <div className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100 animate-slide-in-up">
        {/* 对话框头部 - 增强版 */}
        <div className="gradient-flow text-white p-6 relative overflow-hidden">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

          <div className="relative z-10 flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/30 shadow-lg">
              <span className="text-lg font-bold">AI</span>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold mb-1">Temu自动化助手</h3>
              <p className="text-blue-100 text-sm flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>流量加速自动化执行中...</span>
              </p>
            </div>
            {/* 状态指示器 */}
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{animationDelay: '0s'}}></div>
              <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
          </div>
        </div>

        {/* 对话内容 */}
        <div className="p-8">
          {/* AI消息 - 增强版 */}
          <div className="mb-8 animate-slide-in-left">
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 mb-6 border border-gray-200 shadow-sm">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <div className="flex-1">
                  <p className="text-gray-800 mb-3 text-lg">好的，开始执行计划</p>
                  <div className="bg-white rounded-xl p-4 border border-blue-200 shadow-sm">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="font-semibold text-gray-700">执行配置</span>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      高级流量加权档位，价格范围4-6美元，时效30天
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 增强版时间线步骤列表 - 完美连接线 + 丰富视觉效果 */}
          <div className="relative">
            {steps.slice(0, visibleSteps).map((step, index) => {
              const animationClass = stepAnimations[index];
              const isCurrentStep = currentStep === index && workflowStatus === 'running';

              return (
                <div
                  key={step.id}
                  className={`relative ${
                    animationClass === 'slide-in' ? 'animate-slide-in-up' :
                    animationClass === 'shake' ? 'animate-shake' :
                    animationClass === 'bounce' ? 'animate-bounce-in' : ''
                  }`}
                >
                  {/* 增强版连接线 - 渐变 + 发光效果 */}
                  {index < visibleSteps - 1 && (
                    <div className="absolute left-4 top-8 w-0.5 z-0" style={{ height: 'calc(100% + 1.5rem)' }}>
                      {/* 背景连接线 */}
                      <div className="absolute inset-0 bg-gray-200 rounded-full"></div>
                      {/* 进度连接线 */}
                      <div
                        className={`absolute inset-0 rounded-full transition-all duration-700 connection-line-glow ${
                          step.status === 'completed' ? 'bg-gradient-to-b from-green-400 to-green-600' :
                          step.status === 'failed' ? 'bg-gradient-to-b from-red-400 to-red-600' :
                          step.status === 'running' ? 'bg-gradient-to-b from-blue-400 to-blue-600 animate-pulse-glow' :
                          'bg-gray-300'
                        }`}
                        style={{
                          transform: step.status === 'completed' ? 'scaleY(1)' :
                                   step.status === 'running' ? 'scaleY(0.7)' : 'scaleY(0.3)',
                          transformOrigin: 'top'
                        }}
                      ></div>
                    </div>
                  )}

                  {/* 增强版步骤内容容器 */}
                  <div className="flex items-start space-x-6 relative z-10 pb-8">
                    {/* 增强版状态图标容器 */}
                    <div className="flex-shrink-0 relative">
                      {/* 外圈发光效果 */}
                      <div className={`absolute inset-0 rounded-full transition-all duration-500 ${
                        step.status === 'running' ? 'animate-pulse-glow' : ''
                      }`}></div>

                      {/* 图标背景圆圈 - 增强版 */}
                      <div className="w-12 h-12 bg-white rounded-full absolute inset-0 z-10 border-2 border-gray-100 shadow-lg"></div>

                      {/* 状态图标 - 增强版 */}
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 relative z-20 shadow-lg transform ${
                        animationClass === 'pulse' ? 'animate-pulse-glow' :
                        animationClass === 'bounce' ? 'animate-bounce-in' : ''
                      } ${
                        step.status === 'completed' ? 'bg-gradient-to-br from-green-400 to-green-600 text-white scale-110' :
                        step.status === 'failed' ? 'bg-gradient-to-br from-red-400 to-red-600 text-white' :
                        step.status === 'running' ? 'bg-gradient-to-br from-blue-400 to-blue-600 text-white animate-pulse-glow' :
                        'bg-gradient-to-br from-gray-300 to-gray-400 text-gray-600'
                      }`}>
                        {step.status === 'completed' && <CheckCircle className="w-6 h-6" />}
                        {step.status === 'failed' && <XCircle className="w-6 h-6" />}
                        {step.status === 'running' && <Loader2 className="w-6 h-6 animate-spin" />}
                        {step.status === 'pending' && <div className="w-4 h-4 rounded-full bg-gray-600"></div>}
                      </div>

                      {/* 进度环 */}
                      {step.status === 'running' && (
                        <div className="absolute inset-0 w-12 h-12 rounded-full border-2 border-blue-200">
                          <div className="absolute inset-0 w-12 h-12 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
                        </div>
                      )}
                    </div>

                    {/* 增强版步骤内容 */}
                    <div className="flex-1 min-w-0 pt-2">
                      <div className="mb-4">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-xl font-semibold text-gray-900">{step.name}</h4>
                          {/* 状态徽章 */}
                          <div className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
                            step.status === 'completed' ? 'bg-green-100 text-green-700 animate-bounce-in' :
                            step.status === 'failed' ? 'bg-red-100 text-red-700 animate-shake' :
                            step.status === 'running' ? 'bg-blue-100 text-blue-700 animate-pulse' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {step.status === 'completed' ? '✓ 已完成' :
                             step.status === 'failed' ? '✗ 失败' :
                             step.status === 'running' ? '⟳ 执行中' : '⏳ 等待'}
                          </div>
                        </div>

                        {/* 失败状态的重试按钮 - 增强版 */}
                        {step.status === 'failed' && (
                          <button
                            onClick={() => handleButtonClick('retry', retryFromFailed)}
                            disabled={isRunning}
                            className={`mt-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:opacity-50 text-white px-6 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                              buttonPressed === 'retry' ? 'button-press' : ''
                            }`}
                          >
                            <div className="flex items-center space-x-2">
                              <RefreshCw className="w-4 h-4" />
                              <span>重试执行</span>
                            </div>
                          </button>
                        )}
                      </div>

                      {/* 增强版描述文本 */}
                      <div className={`bg-white rounded-xl p-4 border-l-4 transition-all duration-500 ${
                        step.status === 'completed' ? 'border-green-500 bg-green-50/50' :
                        step.status === 'failed' ? 'border-red-500 bg-red-50/50' :
                        step.status === 'running' ? 'border-blue-500 bg-blue-50/50 animate-pulse-glow' :
                        'border-gray-300 bg-gray-50/50'
                      }`}>
                        <p className="text-gray-700 leading-relaxed">
                          {getStepDescription(step)}
                        </p>

                        {/* 进度条 */}
                        {step.status === 'running' && (
                          <div className="mt-3">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full progress-bar"></div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* 失败时的错误信息 - 增强版 */}
                      {step.status === 'failed' && (
                        <div className="mt-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4 animate-shake">
                          <div className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                              <XCircle className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <h5 className="font-medium text-red-800 mb-1">执行失败</h5>
                              <p className="text-red-700 text-sm leading-relaxed">
                                可能是网络连接问题或系统繁忙，请点击重试继续执行。
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* 增强版操作按钮区域 */}
          <div className="mt-12 pt-8 border-t border-gradient-to-r from-transparent via-gray-200 to-transparent">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {workflowStatus === 'idle' && (
                  <button
                    onClick={() => handleButtonClick('start', executeWorkflow)}
                    disabled={isRunning}
                    className={`bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-3 ${
                      buttonPressed === 'start' ? 'button-press' : ''
                    }`}
                  >
                    <Play className="w-5 h-5" />
                    <span>开始执行</span>
                    <div className="w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
                  </button>
                )}

                {workflowStatus === 'running' && (
                  <button
                    disabled
                    className="bg-gradient-to-r from-gray-400 to-gray-500 text-white px-8 py-4 rounded-xl text-lg font-semibold cursor-not-allowed flex items-center space-x-3 animate-pulse-glow"
                  >
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>执行中...</span>
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{animationDelay: '0s'}}></div>
                      <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                      <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  </button>
                )}

                {workflowStatus === 'completed' && (
                  <button
                    onClick={() => handleButtonClick('completed', resetWorkflow)}
                    className={`bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-3 ${
                      showCelebration ? 'animate-celebration' : ''
                    } ${buttonPressed === 'completed' ? 'button-press' : ''}`}
                  >
                    <CheckCircle className="w-5 h-5" />
                    <span>执行完成</span>
                    <div className="w-2 h-2 bg-white/50 rounded-full animate-bounce"></div>
                  </button>
                )}

                {workflowStatus === 'failed' && (
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => handleButtonClick('retry-main', retryFromFailed)}
                      disabled={isRunning}
                      className={`bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:opacity-50 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2 ${
                        buttonPressed === 'retry-main' ? 'button-press' : ''
                      }`}
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>重试</span>
                    </button>
                    <button
                      onClick={() => handleButtonClick('reset', resetWorkflow)}
                      className={`bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                        buttonPressed === 'reset' ? 'button-press' : ''
                      }`}
                    >
                      重新开始
                    </button>
                  </div>
                )}

                {/* 增强版整体进度指示 */}
                {workflowStatus === 'running' && (
                  <div className="flex items-center space-x-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl px-6 py-4 border border-blue-200 shadow-sm animate-pulse-glow">
                    <div className="relative">
                      <Loader2 className="w-6 h-6 text-blue-500 animate-spin" />
                      <div className="absolute inset-0 w-6 h-6 border-2 border-blue-200 rounded-full animate-ping"></div>
                    </div>
                    <div className="flex-1">
                      <div className="text-blue-700 font-semibold mb-1">
                        正在执行第 {currentStep + 1} 步
                      </div>
                      <div className="w-full bg-blue-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-blue-600 text-sm font-medium">
                      {Math.round(((currentStep + 1) / steps.length) * 100)}%
                    </div>
                  </div>
                )}
              </div>

              {/* 增强版刷新按钮 */}
              <button
                onClick={() => handleButtonClick('refresh', resetWorkflow)}
                className={`p-3 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-xl transition-all duration-300 transform hover:scale-110 ${
                  buttonPressed === 'refresh' ? 'button-press' : ''
                }`}
                title="重新开始"
              >
                <RefreshCw className="w-5 h-5" />
              </button>
            </div>

            {/* 增强版状态指示器 */}
            {workflowStatus === 'completed' && (
              <div className={`mt-8 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 shadow-lg ${
                showCelebration ? 'animate-celebration' : 'animate-bounce-in'
              }`}>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center animate-bounce-in">
                    <CheckCircle className="w-8 h-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-green-800 font-bold text-lg mb-1">🎉 执行完成！</h3>
                    <p className="text-green-700 font-medium">
                      自动化执行完成！所有商品已成功配置流量加速
                    </p>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0s'}}></div>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}

            {workflowStatus === 'failed' && failedStep !== null && (
              <div className="mt-8 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-2xl p-6 shadow-lg animate-shake">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                    <AlertCircle className="w-8 h-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-red-800 font-bold text-lg mb-1">⚠️ 执行中断</h3>
                    <p className="text-red-700 font-medium">
                      第{failedStep + 1}步出现问题，可点击重试继续执行
                    </p>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" style={{animationDelay: '0s'}}></div>
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 增强版Demo控制面板 */}
      <div className="mt-8 bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-xl p-6 border border-gray-200 animate-slide-in-up">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">🎮</span>
          </div>
          <h4 className="text-xl font-bold text-gray-900">Demo控制面板</h4>
        </div>

        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6 border border-blue-200">
          <h5 className="font-semibold text-gray-800 mb-2">✨ 增强特性展示</h5>
          <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>完美连接线 + 发光效果</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>智能重试 + 动画反馈</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>渐进显示 + 平滑过渡</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>庆祝动画 + 视觉增强</span>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => handleButtonClick('demo-execute', executeWorkflow)}
            disabled={isRunning}
            className={`bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2 ${
              buttonPressed === 'demo-execute' ? 'button-press' : ''
            }`}
          >
            <span>🚀</span>
            <span>模拟执行</span>
          </button>

          <button
            onClick={() => handleButtonClick('demo-reset', resetWorkflow)}
            className={`bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2 ${
              buttonPressed === 'demo-reset' ? 'button-press' : ''
            }`}
          >
            <span>🔄</span>
            <span>重置状态</span>
          </button>

          <button
            onClick={() => {
              handleButtonClick('demo-fail', () => {
                // 快速演示失败场景
                setVisibleSteps(3);
                setSteps(prev => prev.map((step, index) => {
                  if (index === 0) return { ...step, status: 'completed' };
                  if (index === 1) return { ...step, status: 'completed' };
                  if (index === 2) return { ...step, status: 'failed' };
                  return step;
                }));
                setStepAnimations({ 2: 'shake' });
                setWorkflowStatus('failed');
                setFailedStep(2);
              });
            }}
            className={`bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-2 ${
              buttonPressed === 'demo-fail' ? 'button-press' : ''
            }`}
          >
            <span>⚠️</span>
            <span>演示失败</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TemuWorkflowDemo;
